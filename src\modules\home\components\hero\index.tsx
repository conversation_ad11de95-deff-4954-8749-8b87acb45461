import { Github } from "@medusajs/icons"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@medusajs/ui"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

const Hero = () => {
  return (
    <div className="relative h-screen w-full overflow-hidden bg-casetify-secondary">
      {/* CASETiFY-inspired dynamic background */}
      <div className="absolute inset-0 bg-gradient-to-br from-casetify-neutral-50 via-white to-casetify-neutral-100">
        {/* Animated gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-casetify-accent-blue/10 via-casetify-accent-purple/10 to-casetify-accent-pink/10 animate-gradient-shift bg-[length:200%_200%]" />
      </div>

      {/* Bold geometric shapes - CASETiFY style */}
      <div className="absolute top-20 left-10 w-80 h-80 bg-gradient-to-br from-casetify-accent-blue/20 to-casetify-accent-purple/20 rounded-3xl blur-2xl animate-float" />
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-tl from-casetify-accent-pink/20 to-casetify-accent-orange/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }} />
      <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-casetify-accent-green/30 rounded-2xl rotate-45 animate-scale-in" style={{ animationDelay: '0.5s' }} />
      
      {/* Main content - CASETiFY inspired layout */}
      <div className="relative z-10 flex flex-col justify-center items-center text-center h-full px-4 max-w-7xl mx-auto">
        <div className="space-y-12 animate-slide-up">
          {/* Bold brand statement */}
          <div className="space-y-6">
            <div className="inline-flex items-center px-6 py-3 bg-casetify-primary text-casetify-secondary rounded-full font-medium text-sm tracking-wide uppercase animate-scale-in">
              <span>Show Your Colors</span>
            </div>

            <Heading
              level="h1"
              className="text-6xl md:text-8xl lg:text-9xl font-black text-casetify-primary leading-none tracking-tight"
            >
              SparkCore
            </Heading>

            <div className="relative">
              <Heading
                level="h2"
                className="text-2xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-casetify-accent-blue via-casetify-accent-purple to-casetify-accent-pink bg-clip-text text-transparent leading-tight"
              >
                Quality Products for Everyday Life
              </Heading>
              {/* Underline accent */}
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-casetify-accent-blue to-casetify-accent-pink rounded-full"></div>
            </div>
          </div>

          {/* Bold description */}
          <p className="text-xl md:text-2xl text-casetify-neutral-700 max-w-3xl mx-auto leading-relaxed font-medium">
            Discover amazing products for home, kids & lifestyle.
            <span className="text-casetify-accent-blue font-bold">Available on Amazon</span>,
            <span className="text-casetify-accent-pink font-bold"> TikTok Shop</span>, and
            <span className="text-casetify-accent-purple font-bold"> our store</span>.
          </p>

          {/* CASETiFY-style product categories */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <div className="group relative p-8 bg-casetify-secondary rounded-3xl border-2 border-casetify-neutral-200 hover:border-casetify-accent-blue transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-casetify-accent-blue/5 to-casetify-accent-purple/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="text-5xl mb-4 group-hover:animate-float">🏠</div>
                <div className="text-xl font-bold text-casetify-primary mb-2 group-hover:text-casetify-accent-blue transition-colors">Home & Living</div>
                <div className="text-casetify-neutral-600 font-medium">Beautiful accessories for your space</div>
              </div>
            </div>
            <div className="group relative p-8 bg-casetify-secondary rounded-3xl border-2 border-casetify-neutral-200 hover:border-casetify-accent-pink transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-casetify-accent-pink/5 to-casetify-accent-orange/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="text-5xl mb-4 group-hover:animate-float">👶</div>
                <div className="text-xl font-bold text-casetify-primary mb-2 group-hover:text-casetify-accent-pink transition-colors">Kids' Items</div>
                <div className="text-casetify-neutral-600 font-medium">Safe, fun products for little ones</div>
              </div>
            </div>
            <div className="group relative p-8 bg-casetify-secondary rounded-3xl border-2 border-casetify-neutral-200 hover:border-casetify-accent-purple transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-casetify-accent-purple/5 to-casetify-accent-green/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="text-5xl mb-4 group-hover:animate-float">🎯</div>
                <div className="text-xl font-bold text-casetify-primary mb-2 group-hover:text-casetify-accent-purple transition-colors">Lifestyle Gadgets</div>
                <div className="text-casetify-neutral-600 font-medium">Smart solutions for modern living</div>
              </div>
            </div>
          </div>

          {/* CASETiFY-style CTA Buttons */}
          <div className="flex flex-col gap-6 justify-center items-center">
            <LocalizedClientLink href="/store" className="w-full max-w-md">
              <Button
                size="xlarge"
                className="w-full bg-casetify-primary hover:bg-casetify-neutral-800 text-casetify-secondary border-0 px-10 py-5 text-xl font-bold rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-105"
              >
                Shop Our Products
              </Button>
            </LocalizedClientLink>
            <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
              <a
                href="https://amazon.com/stores/sparkcore"
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1"
              >
                <Button
                  variant="secondary"
                  size="large"
                  className="w-full bg-casetify-secondary border-2 border-casetify-primary text-casetify-primary hover:bg-casetify-primary hover:text-casetify-secondary px-6 py-4 text-lg font-bold rounded-2xl transition-all duration-300 hover:scale-105"
                >
                  Amazon Store
                </Button>
              </a>
              <a
                href="https://shop.tiktok.com/@sparkcore"
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1"
              >
                <Button
                  variant="secondary"
                  size="large"
                  className="w-full bg-gradient-to-r from-casetify-accent-pink to-casetify-accent-purple text-casetify-secondary border-0 px-6 py-4 text-lg font-bold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl"
                >
                  TikTok Shop
                </Button>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* CASETiFY-style scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-8 h-12 border-2 border-casetify-primary rounded-full flex justify-center bg-casetify-secondary/80 backdrop-blur-sm">
          <div className="w-2 h-4 bg-casetify-primary rounded-full mt-2 animate-float"></div>
        </div>
      </div>
    </div>
  )
}

export default Hero
