import { Github } from "@medusajs/icons"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@medusajs/ui"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

const Hero = () => {
  return (
    <div className="relative h-screen w-full overflow-hidden bg-casetify-secondary">
      {/* CASETiFY-style video background placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-casetify-neutral-100 to-casetify-secondary">
        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-5 bg-[radial-gradient(circle_at_1px_1px,_rgba(0,0,0,0.15)_1px,_transparent_0)] bg-[length:20px_20px]" />
      </div>

      {/* Minimal geometric accents - more like CASETiFY */}
      <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-casetify-primary rounded-full animate-float" />
      <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-casetify-primary rounded-full animate-float" style={{ animationDelay: '1s' }} />
      <div className="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-casetify-primary rounded-full animate-float" style={{ animationDelay: '0.5s' }} />
      
      {/* CASETiFY-style main content */}
      <div className="relative z-10 flex flex-col justify-center items-center text-center h-full px-4 max-w-6xl mx-auto">
        <div className="space-y-8 animate-slide-up">
          {/* CASETiFY-style tagline */}
          <div className="space-y-4">
            <div className="inline-flex items-center px-4 py-2 bg-casetify-primary text-casetify-secondary rounded-full font-bold text-xs tracking-widest uppercase">
              <span>Show Your Style</span>
            </div>

            <Heading
              level="h1"
              className="text-5xl md:text-7xl lg:text-8xl font-black text-casetify-primary leading-none tracking-tight"
            >
              SparkCore
            </Heading>

            <Heading
              level="h2"
              className="text-xl md:text-2xl lg:text-3xl font-medium text-casetify-neutral-700 leading-relaxed max-w-2xl mx-auto"
            >
              Quality Products for Everyday Life
            </Heading>
          </div>

          {/* CASETiFY-style simple description */}
          <p className="text-lg md:text-xl text-casetify-neutral-600 max-w-2xl mx-auto leading-relaxed">
            Discover amazing products for home, kids & lifestyle.
            Available on Amazon, TikTok Shop, and our store.
          </p>

          {/* Simplified product categories - CASETiFY style */}
          <div className="flex flex-wrap justify-center gap-4 max-w-3xl mx-auto">
            <div className="group px-6 py-3 bg-casetify-secondary border border-casetify-neutral-300 rounded-full hover:border-casetify-primary transition-all duration-300 cursor-pointer">
              <span className="text-sm font-medium text-casetify-neutral-700 group-hover:text-casetify-primary">🏠 Home & Living</span>
            </div>
            <div className="group px-6 py-3 bg-casetify-secondary border border-casetify-neutral-300 rounded-full hover:border-casetify-primary transition-all duration-300 cursor-pointer">
              <span className="text-sm font-medium text-casetify-neutral-700 group-hover:text-casetify-primary">👶 Kids' Items</span>
            </div>
            <div className="group px-6 py-3 bg-casetify-secondary border border-casetify-neutral-300 rounded-full hover:border-casetify-primary transition-all duration-300 cursor-pointer">
              <span className="text-sm font-medium text-casetify-neutral-700 group-hover:text-casetify-primary">🎯 Lifestyle Gadgets</span>
            </div>
          </div>

          {/* CASETiFY-style minimal CTA */}
          <div className="flex flex-col gap-4 justify-center items-center">
            <LocalizedClientLink href="/store">
              <Button
                size="large"
                className="bg-casetify-primary hover:bg-casetify-neutral-800 text-casetify-secondary border-0 px-8 py-3 text-lg font-bold rounded-full transition-all duration-300"
              >
                Shop Our Products
              </Button>
            </LocalizedClientLink>
            <div className="flex gap-6 text-sm">
              <a
                href="https://amazon.com/stores/sparkcore"
                target="_blank"
                rel="noopener noreferrer"
                className="text-casetify-neutral-600 hover:text-casetify-primary transition-colors underline"
              >
                Amazon Store
              </a>
              <a
                href="https://shop.tiktok.com/@sparkcore"
                target="_blank"
                rel="noopener noreferrer"
                className="text-casetify-neutral-600 hover:text-casetify-primary transition-colors underline"
              >
                TikTok Shop
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Minimal scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border border-casetify-neutral-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-casetify-neutral-400 rounded-full mt-2"></div>
        </div>
      </div>
    </div>
  )
}

export default Hero
