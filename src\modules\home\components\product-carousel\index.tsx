import { Heading } from "@medusajs/ui"

const ProductCarousel = () => {
  const products = [
    {
      id: 1,
      name: "Smart Home Organizer",
      category: "Home & Living",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
      price: "$29.99",
      badge: "Best Seller"
    },
    {
      id: 2,
      name: "Kids Safety Set",
      category: "Kids' Items",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
      price: "$19.99",
      badge: "New"
    },
    {
      id: 3,
      name: "Wireless Charging Pad",
      category: "Lifestyle Gadgets",
      image: "https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400&h=400&fit=crop",
      price: "$39.99",
      badge: "Popular"
    },
    {
      id: 4,
      name: "Minimalist <PERSON><PERSON> Lam<PERSON>",
      category: "Home & Living",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop",
      price: "$49.99",
      badge: "Featured"
    },
    {
      id: 5,
      name: "Educational Toy Set",
      category: "Kids' Items",
      image: "https://images.unsplash.com/photo-1558060370-d644479cb6f7?w=400&h=400&fit=crop",
      price: "$24.99",
      badge: "Safe"
    },
    {
      id: 6,
      name: "Bluetooth Speaker",
      category: "Lifestyle Gadgets",
      image: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400&h=400&fit=crop",
      price: "$59.99",
      badge: "Premium"
    }
  ]

  return (
    <section className="py-20 bg-casetify-secondary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* CASETiFY-style section header */}
        <div className="text-center mb-12">
          <Heading
            level="h2"
            className="text-4xl md:text-5xl font-black text-casetify-primary mb-4"
          >
            Featured Products
          </Heading>
          <p className="text-lg text-casetify-neutral-600 max-w-2xl mx-auto">
            Discover our most popular items across all categories
          </p>
        </div>

        {/* CASETiFY-style horizontal scroll */}
        <div className="relative">
          <div className="flex gap-6 overflow-x-auto pb-4 scrollbar-hide">
            {products.map((product, index) => (
              <div
                key={product.id}
                className="group flex-shrink-0 w-80 bg-casetify-secondary border border-casetify-neutral-200 rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  {/* Badge */}
                  <div className="absolute top-3 left-3 px-3 py-1 bg-casetify-primary text-casetify-secondary text-xs font-bold rounded-full">
                    {product.badge}
                  </div>
                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-casetify-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="text-xs font-medium text-casetify-neutral-500 uppercase tracking-wide mb-2">
                    {product.category}
                  </div>
                  <Heading
                    level="h3"
                    className="text-lg font-bold text-casetify-primary mb-2 group-hover:text-casetify-neutral-700 transition-colors"
                  >
                    {product.name}
                  </Heading>
                  <div className="text-xl font-black text-casetify-primary">
                    {product.price}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Scroll indicators */}
          <div className="flex justify-center mt-8 gap-2">
            {Array.from({ length: Math.ceil(products.length / 3) }).map((_, index) => (
              <div
                key={index}
                className="w-2 h-2 rounded-full bg-casetify-neutral-300 hover:bg-casetify-primary transition-colors cursor-pointer"
              />
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <a
            href="/store"
            className="inline-flex items-center px-8 py-3 bg-casetify-primary text-casetify-secondary font-bold rounded-full hover:bg-casetify-neutral-800 transition-colors"
          >
            View All Products
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      </div>

      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  )
}

export default ProductCarousel
