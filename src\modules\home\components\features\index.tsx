import { Heading } from "@medusajs/ui"

const Features = () => {
  const features = [
    {
      title: "Quality Assurance",
      description: "Every product is carefully tested and sourced from trusted manufacturers to ensure the highest quality standards.",
      icon: "✨",
      iconBg: "from-orange-400/20 to-orange-400/10 border-orange-400/30",
      titleColor: "group-hover:text-orange-500",
      bgGradient: "from-orange-400/10 to-orange-400/5",
      accent: "bg-orange-500"
    },
    {
      title: "Fast Shipping",
      description: "Quick and reliable delivery through Amazon Prime, TikTok Shop logistics, and our optimized fulfillment network.",
      icon: "🚚",
      iconBg: "from-green-400/20 to-green-400/10 border-green-400/30",
      titleColor: "group-hover:text-green-500",
      bgGradient: "from-green-400/10 to-green-400/5",
      accent: "bg-green-500"
    },
    {
      title: "Customer First",
      description: "Our dedicated customer service team ensures your satisfaction with every purchase and resolves any concerns quickly.",
      icon: "❤️",
      iconBg: "from-pink-400/20 to-pink-400/10 border-pink-400/30",
      titleColor: "group-hover:text-pink-500",
      bgGradient: "from-pink-400/10 to-pink-400/5",
      accent: "bg-pink-500"
    },
    {
      title: "Innovative Design",
      description: "We design products that blend functionality with style, making everyday items both useful and beautiful.",
      icon: "🎨",
      iconBg: "from-blue-400/20 to-blue-400/10 border-blue-400/30",
      titleColor: "group-hover:text-blue-500",
      bgGradient: "from-blue-400/10 to-blue-400/5",
      accent: "bg-blue-500"
    },
    {
      title: "Multi-Platform Presence",
      description: "Shop where you love - find our products on Amazon, TikTok Shop, and our own Shopify store for maximum convenience.",
      icon: "🛍️",
      iconBg: "from-purple-400/20 to-purple-400/10 border-purple-400/30",
      titleColor: "group-hover:text-purple-500",
      bgGradient: "from-purple-400/10 to-purple-400/5",
      accent: "bg-purple-500"
    },
    {
      title: "Safe & Trusted",
      description: "We prioritize safety and compliance, ensuring all products meet strict safety standards and regulations.",
      icon: "🛡️",
      iconBg: "from-red-400/20 to-red-400/10 border-red-400/30",
      titleColor: "group-hover:text-red-500",
      bgGradient: "from-red-400/10 to-red-400/5",
      accent: "bg-red-500"
    }
  ]

  return (
    <section className="py-20 bg-casetify-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Simplified Section Header */}
        <div className="text-center mb-16">
          <Heading
            level="h2"
            className="text-4xl md:text-5xl font-black text-casetify-primary mb-6"
          >
            Why Choose SparkCore
          </Heading>
          <p className="text-lg text-casetify-neutral-600 max-w-3xl mx-auto">
            From concept to your doorstep, we're committed to bringing you exceptional products
            that enhance your daily life and bring joy to your home.
          </p>
        </div>

        {/* Simplified Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group p-8 bg-casetify-secondary rounded-2xl border border-casetify-neutral-200 hover:border-casetify-primary hover:shadow-lg transition-all duration-300"
            >
              {/* Icon */}
              <div className="text-4xl mb-4">
                {feature.icon}
              </div>

              {/* Title */}
              <Heading
                level="h3"
                className="text-xl font-bold text-casetify-primary mb-3"
              >
                {feature.title}
              </Heading>

              {/* Description */}
              <p className="text-casetify-neutral-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Simplified Product Categories */}
        <div className="mt-20 text-center">
          <Heading
            level="h3"
            className="text-3xl font-bold text-casetify-primary mb-8"
          >
            Shop by Category
          </Heading>
          <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
            <div className="px-6 py-3 bg-casetify-secondary border border-casetify-neutral-300 rounded-full hover:border-casetify-primary transition-colors cursor-pointer">
              <span className="text-sm font-medium text-casetify-neutral-700">🏠 Home & Living</span>
            </div>
            <div className="px-6 py-3 bg-casetify-secondary border border-casetify-neutral-300 rounded-full hover:border-casetify-primary transition-colors cursor-pointer">
              <span className="text-sm font-medium text-casetify-neutral-700">👶 Kids' Items</span>
            </div>
            <div className="px-6 py-3 bg-casetify-secondary border border-casetify-neutral-300 rounded-full hover:border-casetify-primary transition-colors cursor-pointer">
              <span className="text-sm font-medium text-casetify-neutral-700">🎯 Lifestyle Gadgets</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Features