import { Heading } from "@medusajs/ui"

const Features = () => {
  const features = [
    {
      title: "Quality Assurance",
      description: "Every product is carefully tested and sourced from trusted manufacturers to ensure the highest quality standards.",
      icon: "✨",
      iconBg: "from-orange-400/20 to-orange-400/10 border-orange-400/30",
      titleColor: "group-hover:text-orange-500",
      bgGradient: "from-orange-400/10 to-orange-400/5",
      accent: "bg-orange-500"
    },
    {
      title: "Fast Shipping",
      description: "Quick and reliable delivery through Amazon Prime, TikTok Shop logistics, and our optimized fulfillment network.",
      icon: "🚚",
      iconBg: "from-green-400/20 to-green-400/10 border-green-400/30",
      titleColor: "group-hover:text-green-500",
      bgGradient: "from-green-400/10 to-green-400/5",
      accent: "bg-green-500"
    },
    {
      title: "Customer First",
      description: "Our dedicated customer service team ensures your satisfaction with every purchase and resolves any concerns quickly.",
      icon: "❤️",
      iconBg: "from-pink-400/20 to-pink-400/10 border-pink-400/30",
      titleColor: "group-hover:text-pink-500",
      bgGradient: "from-pink-400/10 to-pink-400/5",
      accent: "bg-pink-500"
    },
    {
      title: "Innovative Design",
      description: "We design products that blend functionality with style, making everyday items both useful and beautiful.",
      icon: "🎨",
      iconBg: "from-blue-400/20 to-blue-400/10 border-blue-400/30",
      titleColor: "group-hover:text-blue-500",
      bgGradient: "from-blue-400/10 to-blue-400/5",
      accent: "bg-blue-500"
    },
    {
      title: "Multi-Platform Presence",
      description: "Shop where you love - find our products on Amazon, TikTok Shop, and our own Shopify store for maximum convenience.",
      icon: "🛍️",
      iconBg: "from-purple-400/20 to-purple-400/10 border-purple-400/30",
      titleColor: "group-hover:text-purple-500",
      bgGradient: "from-purple-400/10 to-purple-400/5",
      accent: "bg-purple-500"
    },
    {
      title: "Safe & Trusted",
      description: "We prioritize safety and compliance, ensuring all products meet strict safety standards and regulations.",
      icon: "🛡️",
      iconBg: "from-red-400/20 to-red-400/10 border-red-400/30",
      titleColor: "group-hover:text-red-500",
      bgGradient: "from-red-400/10 to-red-400/5",
      accent: "bg-red-500"
    }
  ]

  return (
    <section className="py-32 bg-casetify-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* CASETiFY-style Section Header */}
        <div className="text-center mb-24 animate-slide-up">
          <div className="inline-flex items-center px-6 py-3 bg-casetify-primary text-casetify-secondary rounded-full font-bold text-sm tracking-wide uppercase mb-8">
            <span>Why Choose SparkCore</span>
          </div>
          <Heading
            level="h2"
            className="text-5xl md:text-7xl font-black text-casetify-primary mb-8 leading-tight"
          >
            Quality You Can Trust
          </Heading>
          <p className="text-2xl text-casetify-neutral-700 max-w-4xl mx-auto font-medium leading-relaxed">
            From concept to your doorstep, we're committed to bringing you
            <span className="text-casetify-accent-blue font-bold"> exceptional products</span> that
            <span className="text-casetify-accent-purple font-bold"> enhance your daily life</span> and
            <span className="text-casetify-accent-pink font-bold"> bring joy to your home</span>.
          </p>
        </div>

        {/* CASETiFY-style Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group relative p-10 bg-casetify-secondary rounded-3xl border-2 border-casetify-neutral-200 hover:border-casetify-primary hover:shadow-2xl transition-all duration-500 cursor-pointer hover:-translate-y-3 animate-scale-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* CASETiFY-style background gradient on hover */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgGradient} opacity-0 group-hover:opacity-100 rounded-3xl transition-opacity duration-500`} />

              {/* Content */}
              <div className="relative z-10">
                {/* Bold Icon Container */}
                <div className={`w-20 h-20 rounded-3xl bg-gradient-to-br ${feature.iconBg} border-2 flex items-center justify-center text-3xl mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500`}>
                  {feature.icon}
                </div>

                {/* Bold Title */}
                <Heading
                  level="h3"
                  className={`text-2xl font-black text-casetify-primary mb-6 ${feature.titleColor} transition-colors duration-300`}
                >
                  {feature.title}
                </Heading>

                {/* Description */}
                <p className="text-casetify-neutral-700 leading-relaxed font-medium text-lg">
                  {feature.description}
                </p>
              </div>

              {/* CASETiFY-style corner accent */}
              <div className={`absolute top-4 right-4 w-3 h-3 ${feature.accent} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
            </div>
          ))}
        </div>

        {/* CASETiFY-style Product Categories Showcase */}
        <div className="mt-32 text-center">
          <Heading
            level="h3"
            className="text-4xl md:text-5xl font-black text-casetify-primary mb-16"
          >
            Our Product Categories
          </Heading>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="group relative p-10 bg-gradient-to-br from-casetify-accent-blue/10 to-casetify-accent-purple/10 rounded-3xl border-2 border-casetify-accent-blue/20 hover:border-casetify-accent-blue hover:shadow-2xl transition-all duration-500 cursor-pointer hover:-translate-y-2">
              <div className="text-6xl mb-6 group-hover:animate-float">🏠</div>
              <h4 className="text-2xl font-black text-casetify-primary mb-4 group-hover:text-casetify-accent-blue transition-colors">Home & Living</h4>
              <p className="text-casetify-neutral-700 font-medium text-lg">Decorative accessories, organizers, and functional items for your home</p>
              <div className="absolute top-4 right-4 w-4 h-4 bg-casetify-accent-blue rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="group relative p-10 bg-gradient-to-br from-casetify-accent-pink/10 to-casetify-accent-orange/10 rounded-3xl border-2 border-casetify-accent-pink/20 hover:border-casetify-accent-pink hover:shadow-2xl transition-all duration-500 cursor-pointer hover:-translate-y-2">
              <div className="text-6xl mb-6 group-hover:animate-float">👶</div>
              <h4 className="text-2xl font-black text-casetify-primary mb-4 group-hover:text-casetify-accent-pink transition-colors">Kids' Items</h4>
              <p className="text-casetify-neutral-700 font-medium text-lg">Safe, educational, and fun products designed specifically for children</p>
              <div className="absolute top-4 right-4 w-4 h-4 bg-casetify-accent-pink rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="group relative p-10 bg-gradient-to-br from-casetify-accent-green/10 to-casetify-accent-blue/10 rounded-3xl border-2 border-casetify-accent-green/20 hover:border-casetify-accent-green hover:shadow-2xl transition-all duration-500 cursor-pointer hover:-translate-y-2">
              <div className="text-6xl mb-6 group-hover:animate-float">🎯</div>
              <h4 className="text-2xl font-black text-casetify-primary mb-4 group-hover:text-casetify-accent-green transition-colors">Lifestyle Gadgets</h4>
              <p className="text-casetify-neutral-700 font-medium text-lg">Innovative gadgets that make daily tasks easier and more enjoyable</p>
              <div className="absolute top-4 right-4 w-4 h-4 bg-casetify-accent-green rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Features