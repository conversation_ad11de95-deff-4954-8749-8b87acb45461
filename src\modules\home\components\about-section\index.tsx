import { But<PERSON>, Head<PERSON> } from "@medusajs/ui"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

const AboutSection = () => {
  return (
    <section className="py-32 bg-casetify-secondary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
          {/* CASETiFY-style Content */}
          <div className="space-y-10 animate-slide-up">
            <div className="space-y-8">
              <div className="inline-flex items-center px-6 py-3 bg-casetify-primary text-casetify-secondary rounded-full font-bold text-sm tracking-wide uppercase">
                <span>About SparkCore LLC</span>
              </div>

              <Heading
                level="h2"
                className="text-5xl md:text-6xl font-black text-casetify-primary leading-tight"
              >
                Bringing Quality Products to Your
                <span className="bg-gradient-to-r from-casetify-accent-blue via-casetify-accent-purple to-casetify-accent-pink bg-clip-text text-transparent"> Everyday Life</span>
              </Heading>

              <p className="text-2xl text-casetify-neutral-700 leading-relaxed font-medium">
                SparkCore LLC is a <span className="text-casetify-accent-blue font-bold">U.S.-based e-commerce company</span> dedicated to designing, sourcing,
                and selling everyday consumer products that make life better. We specialize in <span className="text-casetify-accent-purple font-bold">home & living
                accessories</span>, <span className="text-casetify-accent-pink font-bold">kids' items</span>, and <span className="text-casetify-accent-green font-bold">lifestyle gadgets</span> that combine quality, functionality, and style.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-casetify-accent-blue to-casetify-accent-purple rounded-2xl flex items-center justify-center">
                  <span className="text-casetify-secondary text-lg font-bold">✓</span>
                </div>
                <div>
                  <h4 className="font-black text-casetify-primary text-xl mb-2">Carefully Curated Selection</h4>
                  <p className="text-casetify-neutral-700 font-medium text-lg">Every product is thoughtfully selected and tested to meet our high standards for quality and functionality.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-casetify-accent-purple to-casetify-accent-pink rounded-2xl flex items-center justify-center">
                  <span className="text-casetify-secondary text-lg font-bold">✓</span>
                </div>
                <div>
                  <h4 className="font-black text-casetify-primary text-xl mb-2">Multi-Platform Convenience</h4>
                  <p className="text-casetify-neutral-700 font-medium text-lg">Shop easily on Amazon, TikTok Shop, or our Shopify store - wherever you prefer to shop.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6">
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-casetify-accent-pink to-casetify-accent-orange rounded-2xl flex items-center justify-center">
                  <span className="text-casetify-secondary text-lg font-bold">✓</span>
                </div>
                <div>
                  <h4 className="font-black text-casetify-primary text-xl mb-2">Trusted & Safe</h4>
                  <p className="text-casetify-neutral-700 font-medium text-lg">We prioritize safety and compliance, especially for kids' products, and never deal in firearms, adult content, or controlled substances.</p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6">
              <LocalizedClientLink href="/store">
                <Button
                  size="large"
                  className="bg-casetify-primary hover:bg-casetify-neutral-800 text-casetify-secondary border-0 px-10 py-4 text-xl font-bold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl"
                >
                  Shop Our Products
                </Button>
              </LocalizedClientLink>
              <div className="flex gap-4">
                <a
                  href="https://amazon.com/stores/sparkcore"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    variant="secondary"
                    size="large"
                    className="border-2 border-casetify-primary text-casetify-primary hover:bg-casetify-primary hover:text-casetify-secondary px-8 py-4 text-lg font-bold rounded-2xl transition-all duration-300 hover:scale-105"
                  >
                    Amazon Store
                  </Button>
                </a>
                <a
                  href="https://shop.tiktok.com/@sparkcore"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    variant="secondary"
                    size="large"
                    className="bg-gradient-to-r from-casetify-accent-pink to-casetify-accent-purple text-casetify-secondary border-0 px-8 py-4 text-lg font-bold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl"
                  >
                    TikTok Shop
                  </Button>
                </a>
              </div>
            </div>
          </div>

          {/* CASETiFY-style Visual Element */}
          <div className="relative animate-scale-in" style={{ animationDelay: '0.3s' }}>
            <div className="relative z-10 bg-casetify-secondary rounded-3xl p-10 shadow-2xl border-2 border-casetify-neutral-200">
              {/* Company Info */}
              <div className="text-center mb-10">
                <Heading level="h3" className="text-3xl font-black text-casetify-primary mb-4">
                  SparkCore LLC at a Glance
                </Heading>
              </div>

              {/* CASETiFY-style Stats */}
              <div className="grid grid-cols-2 gap-8 mb-10">
                <div className="text-center group">
                  <div className="text-5xl font-black text-casetify-accent-blue mb-3 group-hover:animate-float">🇺🇸</div>
                  <div className="text-casetify-neutral-700 text-lg font-bold">U.S.-Based Company</div>
                </div>
                <div className="text-center group">
                  <div className="text-5xl font-black text-casetify-accent-purple mb-3 group-hover:animate-float">3</div>
                  <div className="text-casetify-neutral-700 text-lg font-bold">Sales Platforms</div>
                </div>
                <div className="text-center group">
                  <div className="text-5xl font-black text-casetify-accent-green mb-3 group-hover:animate-float">100%</div>
                  <div className="text-casetify-neutral-700 text-lg font-bold">Quality Tested</div>
                </div>
                <div className="text-center group">
                  <div className="text-5xl font-black text-casetify-accent-pink mb-3 group-hover:animate-float">0</div>
                  <div className="text-casetify-neutral-700 text-lg font-bold">Prohibited Items</div>
                </div>
              </div>
              
              {/* CASETiFY-style Platform Icons */}
              <div className="border-t-2 border-casetify-neutral-200 pt-8">
                <div className="text-center mb-6">
                  <h4 className="font-black text-casetify-primary text-xl">Find Us On</h4>
                </div>
                <div className="flex justify-center space-x-8">
                  <div className="text-center group cursor-pointer">
                    <div className="w-16 h-16 bg-gradient-to-r from-casetify-accent-orange to-casetify-accent-orange rounded-2xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <span className="text-casetify-secondary font-black text-lg">AMZ</span>
                    </div>
                    <span className="text-sm text-casetify-neutral-700 font-bold">Amazon</span>
                  </div>
                  <div className="text-center group cursor-pointer">
                    <div className="w-16 h-16 bg-gradient-to-r from-casetify-accent-pink to-casetify-accent-pink rounded-2xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <span className="text-casetify-secondary font-black text-lg">TT</span>
                    </div>
                    <span className="text-sm text-casetify-neutral-700 font-bold">TikTok Shop</span>
                  </div>
                  <div className="text-center group cursor-pointer">
                    <div className="w-16 h-16 bg-gradient-to-r from-casetify-accent-green to-casetify-accent-blue rounded-2xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      <span className="text-casetify-secondary font-black text-lg">SF</span>
                    </div>
                    <span className="text-sm text-casetify-neutral-700 font-bold">Shopify</span>
                  </div>
                </div>
              </div>
            </div>

            {/* CASETiFY-style background decorations */}
            <div className="absolute -top-6 -left-6 w-80 h-80 bg-gradient-to-br from-casetify-accent-blue/20 to-casetify-accent-purple/20 rounded-3xl blur-3xl -z-10 animate-float" />
            <div className="absolute -bottom-6 -right-6 w-80 h-80 bg-gradient-to-tl from-casetify-accent-purple/20 to-casetify-accent-pink/20 rounded-full blur-3xl -z-10 animate-float" style={{ animationDelay: '1s' }} />
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutSection